/*
 * services/trainer-spawner.js
 * Advanced NPC Trainer Spawning System
 *
 * FEATURES:
 * - Spawns 5 trainers within 500m radius on real OpenStreetMap ways/paths/roads
 * - Uses Overpass API with 60-minute caching and rate limiting
 * - Weighted sampling: foot paths (3x) > cycle paths (2x) > local roads (1x) > primary roads (0.5x)
 * - Minimum 20m spacing between trainers (fallback to 10m if needed)
 * - Respawn trigger when player moves >250m from last spawn anchor point
 * - Robust fallback to random positioning when OSM data is insufficient
 *
 * OVERPASS INTEGRATION:
 * - POST requests to https://overpass-api.de/api/interpreter
 * - Separate cache namespace "osm-ways" (distinct from landuse cache)
 * - 60-minute cache duration, 200 entry limit
 * - Exponential backoff retry logic (300ms, 900ms)
 *
 * CONFIGURATION:
 * - spawnRadius: 500m (configurable)
 * - desiredTrainerCount: 5 (configurable)
 * - minSpacing: 20m (fallback to 10m)
 * - Tag weights via setTrainerTagWeights()
 *
 * USAGE:
 * const trainers = await trainerSpawner.spawnRandomTrainers(playerL<PERSON>, playerLng, 5);
 *
 * RESPAWN TRIGGER:
 * Integrates with existing >250m movement trigger via lastTrainerSpawnAnchor
 */

import { config } from '../config.js';
import { logger } from '../utils/logger.js';
import { gameState } from '../state/game-state.js';
import Trainer from '../Trainer.js';
import Pokemon from '../Pokemon.js';
import turf from '../lib/turf.js';

// OSM Ways Cache System (separate from landuse cache)
const osmWaysCache = new Map();
const OSM_WAYS_CACHE_DURATION_MS = 60 * 60 * 1000; // 60 minutes
const MAX_CACHE_ENTRIES_OSM_WAYS = 200; // Separate limit from landuse cache
const OSM_WAYS_CACHE_KEY_PREFIX = 'osm_ways_';

/**
 * Generate cache key for OSM ways data
 * @param {number} lat - Latitude
 * @param {number} lng - Longitude
 * @param {number} radius - Radius in meters
 * @returns {string} - Cache key
 */
function generateOSMWaysCacheKey(lat, lng, radius) {
  // Round to 5 decimal places (~1m accuracy) for better cache hits
  const roundedLat = Math.round(lat * 100000) / 100000;
  const roundedLng = Math.round(lng * 100000) / 100000;
  return `${OSM_WAYS_CACHE_KEY_PREFIX}${roundedLat}_${roundedLng}_${radius}`;
}

/**
 * Check if cache entry is valid and not expired
 * @param {Object} entry - Cache entry
 * @returns {boolean} - True if valid
 */
function isValidOSMWaysCacheEntry(entry) {
  if (!entry || !entry.timestamp || !entry.data) return false;
  const age = Date.now() - entry.timestamp;
  return age < OSM_WAYS_CACHE_DURATION_MS;
}

/**
 * Get cached OSM ways result
 * @param {string} cacheKey - Cache key
 * @returns {Object|null} - Cached data or null
 */
function getCachedOSMWaysResult(cacheKey) {
  const memoryCache = osmWaysCache.get(cacheKey);
  if (memoryCache && isValidOSMWaysCacheEntry(memoryCache)) {
    logger.debug(`[OSM-WAYS-CACHE] 💾 Memory cache hit for: ${cacheKey}`);
    return memoryCache.data;
  }

  // Remove expired memory cache entry
  if (memoryCache) {
    osmWaysCache.delete(cacheKey);
    logger.debug(`[OSM-WAYS-CACHE] Expired memory cache entry removed: ${cacheKey}`);
  }

  // Try localStorage fallback
  try {
    const localStorageData = localStorage.getItem(cacheKey);
    if (localStorageData) {
      const parsed = JSON.parse(localStorageData);
      if (isValidOSMWaysCacheEntry(parsed)) {
        logger.debug(`[OSM-WAYS-CACHE] 💿 localStorage cache hit for: ${cacheKey}`);
        // Restore to memory cache for faster access
        osmWaysCache.set(cacheKey, parsed);
        return parsed.data;
      } else {
        // Remove expired localStorage entry
        localStorage.removeItem(cacheKey);
        logger.debug(`[OSM-WAYS-CACHE] Expired localStorage entry removed: ${cacheKey}`);
      }
    }
  } catch (e) {
    logger.warn(`[OSM-WAYS-CACHE] ⚠️ Error reading localStorage cache: ${e.message}`);
  }

  return null;
}

/**
 * Store OSM ways result in cache
 * @param {string} cacheKey - Cache key
 * @param {Object} data - Data to cache
 */
function setCachedOSMWaysResult(cacheKey, data) {
  const cacheEntry = {
    data: data,
    timestamp: Date.now()
  };

  // Store in memory cache with size limit
  if (osmWaysCache.size >= MAX_CACHE_ENTRIES_OSM_WAYS) {
    const oldestKey = osmWaysCache.keys().next().value;
    osmWaysCache.delete(oldestKey);
    logger.debug(`[OSM-WAYS-CACHE] Removed oldest memory cache entry: ${oldestKey}`);
  }

  osmWaysCache.set(cacheKey, cacheEntry);

  // Also store in localStorage for persistence
  try {
    localStorage.setItem(cacheKey, JSON.stringify(cacheEntry));
    logger.debug(`[OSM-WAYS-CACHE] 💾 Cached result in memory and localStorage: ${cacheKey}`);
  } catch (e) {
    logger.warn(`[OSM-WAYS-CACHE] ⚠️ Failed to store in localStorage: ${e.message}`);
    logger.debug(`[OSM-WAYS-CACHE] 💾 Cached result in memory only: ${cacheKey}`);
  }
}

export class TrainerSpawner {
    constructor() {
        this.trainerTypes = null;
        this.npcNames = null;
        this.spawnRadius = config.trainers?.spawnRadius || 500; // 500m radius for OSM-constrained spawning
        this.desiredTrainerCount = config.trainers?.desiredCount || 5; // Number of trainers to spawn
        this.minSpacing = config.trainers?.minSpacing || 20; // Minimum spacing between trainers in meters
        this.minSpacingFallback = config.trainers?.minSpacingFallback || 10; // Fallback minimum spacing

        // Tag-based weighting for OSM ways (configurable via setTrainerTagWeights)
        this.trainerTagWeights = {
            footPriority: 3,      // footway, path, pedestrian, steps, hiking routes
            cyclePriority: 2,     // cycleway, bicycle routes
            localRoadPriority: 1, // residential, service, track, living_street
            primaryRoadPriority: 0.5 // primary, secondary, tertiary roads
        };
    }

    /**
     * Load trainer types and NPC names
     * @returns {Promise<void>}
     */
    async loadData() {
        try {
            // Load trainer types
            if (!this.trainerTypes) {
                const trainerTypesResponse = await fetch('./trainerTypes.json');
                const trainerTypesData = await trainerTypesResponse.json();
                this.trainerTypes = trainerTypesData.trainerTypes;
                logger.debug(`Loaded ${Object.keys(this.trainerTypes).length} trainer types`);
            }

            // Load NPC names
            if (!this.npcNames) {
                const npcNamesResponse = await fetch('./src/NPCNames/NPCNames.json');
                this.npcNames = await npcNamesResponse.json();
                logger.debug(`Loaded ${this.npcNames.male.length} male and ${this.npcNames.female.length} female NPC names`);
            }
        } catch (error) {
            logger.error('Error loading trainer data:', error);
            throw error;
        }
    }





    /**
     * Generate a team based on trainer preferences
     * @param {Array} preferences - Array of preferred Pokemon types
     * @param {Object} levelRange - Level range object with min/max
     * @returns {Promise<Array>} - Array of Pokemon
     */
    async generateTeam(preferences, levelRange) {
        const team = [];
        const teamSize = 6; // Always generate 6 Pokemon

        try {
            // Ensure we have pokedex data
            if (!gameState.pokedexData || gameState.pokedexData.length === 0) {
                logger.warn('No pokedex data available for team generation');
                return team;
            }

            for (let i = 0; i < teamSize; i++) {
                const pokemon = await this.generatePokemonForTeam(preferences, levelRange);
                if (pokemon) {
                    team.push(pokemon);
                }
            }

            logger.debug(`Generated team of ${team.length} Pokemon for trainer`);
            return team;

        } catch (error) {
            logger.error('Error generating trainer team:', error);
            return team;
        }
    }

    /**
     * Generate a single Pokemon for the team
     * @param {Array} preferences - Array of preferred Pokemon types
     * @param {Object} levelRange - Level range object with min/max
     * @returns {Promise<Pokemon|null>} - Generated Pokemon or null
     */
    async generatePokemonForTeam(preferences, levelRange) {
        try {
            // Find Pokemon that match the trainer's type preferences
            const matchingPokemon = gameState.pokedexData.filter(pokeEntry => {
                if (!pokeEntry.types || !Array.isArray(pokeEntry.types)) {
                    return false;
                }

                // Check if any of the Pokemon's types match the trainer's preferences
                return pokeEntry.types.some(pokemonType => {
                    return preferences.some(preferredType => {
                        // Case-insensitive comparison
                        return pokemonType.toLowerCase() === preferredType.toLowerCase();
                    });
                });
            });

            if (matchingPokemon.length === 0) {
                logger.warn('No Pokemon found matching trainer preferences:', preferences);
                // Fallback to any Pokemon
                const randomEntry = gameState.pokedexData[Math.floor(Math.random() * gameState.pokedexData.length)];
                return await this.createPokemonFromEntry(randomEntry, levelRange);
            }

            // Select random Pokemon from matching ones
            const randomEntry = matchingPokemon[Math.floor(Math.random() * matchingPokemon.length)];
            return await this.createPokemonFromEntry(randomEntry, levelRange);

        } catch (error) {
            logger.error('Error generating Pokemon for team:', error);
            return null;
        }
    }

    /**
     * Create a Pokemon from a pokedex entry with proper evolution
     * @param {Object} pokeEntry - Pokedex entry
     * @param {Object} levelRange - Level range object with min/max
     * @returns {Promise<Pokemon|null>} - Created Pokemon or null
     */
    async createPokemonFromEntry(pokeEntry, levelRange) {
        try {
            // Generate random level within range
            const level = Math.floor(Math.random() * (levelRange.max - levelRange.min + 1)) + levelRange.min;

            // Create pokedex snapshot to prevent race conditions (same as Pokemon spawner)
            const pokedexSnapshot = JSON.parse(JSON.stringify(gameState.pokedexData));

            // Calculate evolution data with isolated pokedex snapshot
            const evolutionData = await this.calculateEvolutionData(pokeEntry.name, level, pokedexSnapshot);

            // Create Pokemon with base data first
            const pokemon = new Pokemon(
                pokeEntry.name, // Base name
                pokeEntry.types[0], // Primary type
                level,
                {
                    types: pokeEntry.types,
                    evolution_level: pokeEntry.evolution_level,
                    evolution_item: pokeEntry.evolution_item,
                    dex: pokeEntry.dex_number,
                    image_url: pokeEntry.image_url,
                    rarity: pokeEntry.rarity,
                    evolution_chain_id: pokeEntry.evolution_chain_id
                }
            );

            // Apply evolution data (same pattern as Pokemon spawner)
            pokemon.base_name = pokeEntry.name; // English base name
            pokemon.base_sprite = pokeEntry.image_url;
            pokemon.rarity = pokeEntry.rarity || 'common';

            // Set the evolved form data (keeping English names for internal logic)
            pokemon.name = evolutionData.name; // English evolved name
            pokemon.image_url = evolutionData.sprite;
            pokemon.image = evolutionData.sprite; // Ensure image is set
            pokemon.dex_number = evolutionData.dex_number;
            pokemon.types = evolutionData.types;
            pokemon.evolution_chain_id = evolutionData.evolution_chain_id;

            logger.debug(`Created trainer Pokemon: ${pokemon.name} (Level ${level}) evolved from ${pokeEntry.name}`);
            return pokemon;

        } catch (error) {
            logger.error('Error creating Pokemon from entry:', error);
            return null;
        }
    }

    /**
     * Calculate evolution data with isolated pokedex (same as Pokemon spawner)
     * @param {string} baseName - Base Pokemon name
     * @param {number} level - Pokemon level
     * @param {Array} pokedexSnapshot - Isolated pokedex data snapshot
     * @returns {Promise<Object>} - Evolution data {name, sprite, dex_number, types}
     */
    async calculateEvolutionData(baseName, level, pokedexSnapshot) {
        // Validate input parameters
        if (!baseName || typeof baseName !== 'string') {
            throw new Error(`Invalid baseName for evolution calculation: ${baseName}`);
        }
        if (!level || typeof level !== 'number' || level < 1 || level > 100) {
            throw new Error(`Invalid level for evolution calculation: ${level}`);
        }
        if (!pokedexSnapshot || !Array.isArray(pokedexSnapshot) || pokedexSnapshot.length === 0) {
            throw new Error(`Invalid pokedex snapshot for evolution calculation: ${pokedexSnapshot?.length || 'null'} entries`);
        }

        // Create temporary Pokemon object only for evolution calculation
        const tempPokemon = new Pokemon(baseName, 'normal', level);

        // Calculate evolution with isolated pokedex
        const displayForm = await tempPokemon.getDisplayForm(pokedexSnapshot);

        // Validate evolution result
        if (!displayForm || !displayForm.name || !displayForm.dex_number) {
            logger.error(`Invalid evolution result for ${baseName} level ${level}`);

            // Fallback to base Pokemon data
            const baseEntry = pokedexSnapshot.find(p =>
                p.name.toLowerCase() === baseName.toLowerCase()
            );

            if (baseEntry) {
                return {
                    name: baseEntry.name,
                    sprite: baseEntry.image_url,
                    dex_number: baseEntry.dex_number,
                    types: baseEntry.types,
                    evolution_chain_id: baseEntry.evolution_chain_id
                };
            } else {
                // Ultimate fallback
                return {
                    name: baseName,
                    sprite: '',
                    dex_number: null,
                    types: ['normal'],
                    evolution_chain_id: null
                };
            }
        }

        return {
            name: displayForm.name,
            sprite: displayForm.sprite,
            dex_number: displayForm.dex_number,
            types: displayForm.types,
            evolution_chain_id: displayForm.evolution_chain_id
        };
    }

    /**
     * Configure tag-based weighting for trainer spawning on OSM ways
     * @param {Object} config - Weight configuration object
     * @returns {Object} - Applied weight configuration
     */
    setTrainerTagWeights(config = {}) {
        const defaults = {
            footPriority: 3,      // footway, path, pedestrian, steps, hiking routes
            cyclePriority: 2,     // cycleway, bicycle routes
            localRoadPriority: 1, // residential, service, track, living_street
            primaryRoadPriority: 0.5 // primary, secondary, tertiary roads
        };

        const cleaned = {
            footPriority: Number.isFinite(config.footPriority) && config.footPriority >= 0 ? config.footPriority : defaults.footPriority,
            cyclePriority: Number.isFinite(config.cyclePriority) && config.cyclePriority >= 0 ? config.cyclePriority : defaults.cyclePriority,
            localRoadPriority: Number.isFinite(config.localRoadPriority) && config.localRoadPriority >= 0 ? config.localRoadPriority : defaults.localRoadPriority,
            primaryRoadPriority: Number.isFinite(config.primaryRoadPriority) && config.primaryRoadPriority >= 0 ? config.primaryRoadPriority : defaults.primaryRoadPriority
        };

        this.trainerTagWeights = cleaned;
        logger.debug(`[TRAINER-SPAWNER] Updated tag weights:`, cleaned);
        return this.trainerTagWeights;
    }

    /**
     * Build Overpass QL query for fetching OSM ways suitable for trainer spawning
     * @param {number} lat - Center latitude
     * @param {number} lng - Center longitude
     * @param {number} radius - Search radius in meters
     * @returns {string} - Overpass QL query
     * @throws {Error} - If inputs are invalid or out of range
     */
    buildOverpassQuery(lat, lng, radius) {
        // Validate and sanitize inputs
        const validatedLat = this.validateLatitude(lat);
        const validatedLng = this.validateLongitude(lng);
        const validatedRadius = this.validateRadius(radius);

        // Query based on requirements: exclude motorways/trunks, prioritize foot/cycle paths
        return `[out:json][timeout:25];
(
  way(around:${validatedRadius},${validatedLat},${validatedLng})["highway"]["highway"!="motorway"]["highway"!="trunk"];
  way(around:${validatedRadius},${validatedLat},${validatedLng})["highway"~"path|footway|pedestrian|residential|service|track|living_street|steps"];
  way(around:${validatedRadius},${validatedLat},${validatedLng})["cycleway"];
  relation(around:${validatedRadius},${validatedLat},${validatedLng})["type"="route"]["route"~"hiking|foot|bicycle"];
);
out geom;`;
    }

    /**
     * Validate and sanitize latitude input
     * @param {*} lat - Input latitude value
     * @returns {number} - Validated latitude between -90 and 90
     * @throws {Error} - If latitude is invalid or out of range
     */
    validateLatitude(lat) {
        const numLat = Number(lat);
        if (isNaN(numLat)) {
            throw new Error(`Invalid latitude: ${lat}. Must be a valid number.`);
        }
        if (numLat < -90 || numLat > 90) {
            throw new Error(`Latitude out of range: ${numLat}. Must be between -90 and 90.`);
        }
        return numLat;
    }

    /**
     * Validate and sanitize longitude input
     * @param {*} lng - Input longitude value
     * @returns {number} - Validated longitude between -180 and 180
     * @throws {Error} - If longitude is invalid or out of range
     */
    validateLongitude(lng) {
        const numLng = Number(lng);
        if (isNaN(numLng)) {
            throw new Error(`Invalid longitude: ${lng}. Must be a valid number.`);
        }
        if (numLng < -180 || numLng > 180) {
            throw new Error(`Longitude out of range: ${numLng}. Must be between -180 and 180.`);
        }
        return numLng;
    }

    /**
     * Validate and sanitize radius input
     * @param {*} radius - Input radius value in meters
     * @returns {number} - Validated radius between 1 and 50000 meters
     * @throws {Error} - If radius is invalid or out of range
     */
    validateRadius(radius) {
        const numRadius = Number(radius);
        if (isNaN(numRadius)) {
            throw new Error(`Invalid radius: ${radius}. Must be a valid number.`);
        }
        if (numRadius < 1 || numRadius > 50000) {
            throw new Error(`Radius out of range: ${numRadius}. Must be between 1 and 50000 meters.`);
        }
        return numRadius;
    }

    /**
     * Fetch OSM ways from Overpass API with caching and retry logic
     * @param {number} lat - Center latitude
     * @param {number} lng - Center longitude
     * @param {number} radius - Search radius in meters (default: 500)
     * @returns {Promise<Object>} - Overpass JSON response or empty result
     */
    async fetchOSMWays(lat, lng, radius = 500) {
        const cacheKey = generateOSMWaysCacheKey(lat, lng, radius);

        // Check cache first
        const cachedResult = getCachedOSMWaysResult(cacheKey);
        if (cachedResult) {
            return cachedResult;
        }

        logger.debug(`[OSM-WAYS-CACHE] Cache miss, making API request for: ${cacheKey}`);

        const query = this.buildOverpassQuery(lat, lng, radius);
        const url = 'https://overpass-api.de/api/interpreter';

        // Retry configuration
        const maxRetries = 2;
        const baseDelay = 300; // 300ms base delay

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    // Exponential backoff: 300ms, 900ms
                    const delay = baseDelay * Math.pow(3, attempt - 1);
                    logger.debug(`[OSM-WAYS-CACHE] Retry attempt ${attempt} after ${delay}ms delay`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }

                const startTime = Date.now();
                const response = await fetch(url, {
                    method: 'POST',
                    body: query,
                    headers: { 'Content-Type': 'text/plain' }
                });

                const emptyResult = { elements: [] };

                if (!response.ok) {
                    logger.warn(`[OSM-WAYS-CACHE] API request failed with status: ${response.status} (attempt ${attempt + 1}/${maxRetries + 1})`);
                    if (attempt === maxRetries) {
                        setCachedOSMWaysResult(cacheKey, emptyResult);
                        return emptyResult;
                    }
                    continue; // Retry
                }

                const data = await response.json();
                const duration = Date.now() - startTime;

                logger.debug(`[OSM-WAYS-CACHE] API request completed in ${duration}ms`);
                logger.debug(`[OSM-WAYS-CACHE] Received ${data.elements?.length || 0} OSM elements`);

                if (data.elements && data.elements.length > 0) {
                    const elementTypes = data.elements.reduce((acc, el) => {
                        acc[el.type] = (acc[el.type] || 0) + 1;
                        return acc;
                    }, {});
                    logger.debug(`[OSM-WAYS-CACHE] Element types: ${Object.entries(elementTypes).map(([type, count]) => `${type}(${count})`).join(', ')}`);
                }

                // Cache and return result
                setCachedOSMWaysResult(cacheKey, data);
                return data;

            } catch (error) {
                logger.warn(`[OSM-WAYS-CACHE] API request error (attempt ${attempt + 1}/${maxRetries + 1}):`, error.message);
                if (attempt === maxRetries) {
                    const emptyResult = { elements: [] };
                    setCachedOSMWaysResult(cacheKey, emptyResult);
                    return emptyResult;
                }
            }
        }

        // Should never reach here, but just in case
        const emptyResult = { elements: [] };
        setCachedOSMWaysResult(cacheKey, emptyResult);
        return emptyResult;
    }

    /**
     * Convert Overpass JSON response to GeoJSON LineString features
     * @param {Object} overpassJson - Overpass API response
     * @returns {Array} - Array of GeoJSON LineString features with tags
     */
    toLineStrings(overpassJson) {
        const lineStrings = [];

        if (!overpassJson || !overpassJson.elements || overpassJson.elements.length === 0) {
            logger.debug('[OSM-WAYS] No elements to convert to LineStrings');
            return lineStrings;
        }

        for (const element of overpassJson.elements) {
            try {
                // Handle ways and relations with geometry
                if ((element.type === 'way' || element.type === 'relation') && element.geometry && element.tags) {
                    let coords = null;

                    if (element.type === 'way' && element.geometry && element.geometry.length >= 2) {
                        // Ways have simple geometry array
                        coords = element.geometry.map(node => [node.lon, node.lat]);
                    } else if (element.type === 'relation' && element.geometry) {
                        // Relations might have more complex geometry structure
                        if (Array.isArray(element.geometry) && element.geometry.length >= 2) {
                            coords = element.geometry.map(node => [node.lon, node.lat]);
                        } else if (element.geometry.coordinates) {
                            // Some relations might have GeoJSON-like structure
                            coords = element.geometry.coordinates[0] || element.geometry.coordinates;
                        }
                    }

                    if (coords && coords.length >= 2) {
                        // Create GeoJSON LineString feature
                        const lineString = {
                            type: 'Feature',
                            geometry: {
                                type: 'LineString',
                                coordinates: coords
                            },
                            properties: {
                                osm_id: element.id,
                                element_type: element.type,
                                tags: element.tags,
                                // Extract key highway/cycleway/route tags for weighting
                                highway: element.tags.highway || null,
                                cycleway: element.tags.cycleway || null,
                                route: element.tags.route || null
                            }
                        };

                        lineStrings.push(lineString);
                    }
                }
            } catch (error) {
                logger.warn(`[OSM-WAYS] Error processing element ${element.id}:`, error.message);
                continue;
            }
        }

        logger.debug(`[OSM-WAYS] Converted ${lineStrings.length} elements to LineString features`);

        // Debug: Log highway types found
        const highwayTypes = {};
        lineStrings.forEach(ls => {
            const highway = ls.properties.highway;
            if (highway) {
                highwayTypes[highway] = (highwayTypes[highway] || 0) + 1;
            }
        });

        if (Object.keys(highwayTypes).length > 0) {
            logger.debug(`[OSM-WAYS] Highway types: ${Object.entries(highwayTypes).map(([type, count]) => `${type}(${count})`).join(', ')}`);
        }

        return lineStrings;
    }

    /**
     * Calculate weight for a LineString based on its tags
     * @param {Object} lineString - GeoJSON LineString feature
     * @param {Object} tagWeights - Tag weight configuration
     * @returns {number} - Weight multiplier
     */
    calculateLineWeight(lineString, tagWeights = this.trainerTagWeights) {
        const props = lineString.properties;
        let weight = 1; // Default weight

        // Check highway tags (highest priority)
        if (props.highway) {
            const highway = props.highway.toLowerCase();
            if (['footway', 'path', 'pedestrian', 'steps'].includes(highway)) {
                weight = Math.max(weight, tagWeights.footPriority);
            } else if (highway === 'cycleway') {
                weight = Math.max(weight, tagWeights.cyclePriority);
            } else if (['residential', 'service', 'track', 'living_street'].includes(highway)) {
                weight = Math.max(weight, tagWeights.localRoadPriority);
            } else if (['primary', 'secondary', 'tertiary'].includes(highway)) {
                weight = Math.max(weight, tagWeights.primaryRoadPriority);
            }
        }

        // Check cycleway tags
        if (props.cycleway) {
            weight = Math.max(weight, tagWeights.cyclePriority);
        }

        // Check route tags for relations
        if (props.route) {
            const route = props.route.toLowerCase();
            if (['hiking', 'foot'].includes(route)) {
                weight = Math.max(weight, tagWeights.footPriority);
            } else if (route === 'bicycle') {
                weight = Math.max(weight, tagWeights.cyclePriority);
            }
        }

        return weight;
    }

    /**
     * Sample points on LineStrings using weighted length-proportional sampling
     * @param {Array} lineStrings - Array of GeoJSON LineString features
     * @param {number} neededCount - Number of points to sample
     * @param {number} minSpacingMeters - Minimum spacing between points
     * @param {number} centerLat - Center latitude for distance validation
     * @param {number} centerLng - Center longitude for distance validation
     * @param {number} radiusMeters - Maximum distance from center
     * @param {Object} tagWeights - Tag weight configuration
     * @returns {Array} - Array of sampled points {lat, lng, lineString}
     */
    samplePointsOnLines(lineStrings, neededCount, minSpacingMeters, centerLat, centerLng, radiusMeters, tagWeights = this.trainerTagWeights) {
        if (!lineStrings || lineStrings.length === 0) {
            logger.warn('[OSM-WAYS] No LineStrings available for sampling');
            return [];
        }

        // Calculate weighted lengths for cumulative distribution
        const weightedLines = [];
        let totalWeightedLength = 0;

        for (const lineString of lineStrings) {
            try {
                const length = turf.length(lineString, { units: 'meters' });
                const weight = this.calculateLineWeight(lineString, tagWeights);
                const weightedLength = length * weight;

                if (weightedLength > 0) {
                    weightedLines.push({
                        lineString,
                        length,
                        weight,
                        weightedLength,
                        cumulativeWeight: totalWeightedLength + weightedLength
                    });
                    totalWeightedLength += weightedLength;
                }
            } catch (error) {
                logger.warn(`[OSM-WAYS] Error calculating length for LineString:`, error.message);
                continue;
            }
        }

        if (totalWeightedLength === 0) {
            logger.warn('[OSM-WAYS] No valid weighted LineStrings for sampling');
            return [];
        }

        logger.debug(`[OSM-WAYS] Sampling from ${weightedLines.length} weighted lines, total weighted length: ${totalWeightedLength.toFixed(1)}m`);

        // Sample points using cumulative distribution
        const sampledPoints = [];
        const maxAttempts = neededCount * 10; // Prevent infinite loops
        let attempts = 0;

        while (sampledPoints.length < neededCount && attempts < maxAttempts) {
            attempts++;

            try {
                // Random selection based on weighted cumulative distribution
                const randomWeight = Math.random() * totalWeightedLength;
                const selectedLine = weightedLines.find(wl => wl.cumulativeWeight >= randomWeight);

                if (!selectedLine) continue;

                // Random position along the selected line
                const randomDistance = Math.random() * selectedLine.length;
                const point = turf.along(selectedLine.lineString, randomDistance / 1000, { units: 'kilometers' });

                if (!point || !point.geometry || !point.geometry.coordinates) continue;

                const [lng, lat] = point.geometry.coordinates;

                // Validate distance from center
                const distanceFromCenter = turf.distance(
                    [centerLng, centerLat],
                    [lng, lat],
                    { units: 'meters' }
                );

                if (distanceFromCenter > radiusMeters) continue;

                // Check minimum spacing from existing points
                let tooClose = false;
                for (const existingPoint of sampledPoints) {
                    const spacing = turf.distance(
                        [lng, lat],
                        [existingPoint.lng, existingPoint.lat],
                        { units: 'meters' }
                    );
                    if (spacing < minSpacingMeters) {
                        tooClose = true;
                        break;
                    }
                }

                if (!tooClose) {
                    sampledPoints.push({
                        lat,
                        lng,
                        lineString: selectedLine.lineString,
                        weight: selectedLine.weight,
                        distanceFromCenter
                    });
                }

            } catch (error) {
                logger.warn(`[OSM-WAYS] Error sampling point:`, error.message);
                continue;
            }
        }

        logger.debug(`[OSM-WAYS] Sampled ${sampledPoints.length}/${neededCount} points after ${attempts} attempts`);
        return sampledPoints;
    }

    /**
     * Snap a point to the nearest position on any of the provided LineStrings
     * @param {Object} point - Point to snap {lat, lng}
     * @param {Array} lineStrings - Array of GeoJSON LineString features
     * @returns {Object|null} - Snapped point {lat, lng, lineString} or null if no lines
     */
    snapPointToLine(point, lineStrings) {
        if (!lineStrings || lineStrings.length === 0) {
            logger.warn('[OSM-WAYS] No LineStrings available for snapping');
            return null;
        }

        let closestPoint = null;
        let minDistance = Infinity;
        let closestLineString = null;

        const inputPoint = turf.point([point.lng, point.lat]);

        for (const lineString of lineStrings) {
            try {
                const nearestPoint = turf.nearestPointOnLine(lineString, inputPoint);

                if (nearestPoint && nearestPoint.properties && typeof nearestPoint.properties.dist === 'number') {
                    const distance = nearestPoint.properties.dist * 1000; // Convert km to meters

                    if (distance < minDistance) {
                        minDistance = distance;
                        closestPoint = nearestPoint;
                        closestLineString = lineString;
                    }
                }
            } catch (error) {
                logger.warn(`[OSM-WAYS] Error snapping to LineString:`, error.message);
                continue;
            }
        }

        if (closestPoint && closestPoint.geometry && closestPoint.geometry.coordinates) {
            const [lng, lat] = closestPoint.geometry.coordinates;
            return {
                lat,
                lng,
                lineString: closestLineString,
                snapDistance: minDistance
            };
        }

        return null;
    }

    /**
     * Validate that a position is within the specified radius and optionally snap to nearest line
     * @param {Object} position - Position to validate {lat, lng}
     * @param {number} centerLat - Center latitude
     * @param {number} centerLng - Center longitude
     * @param {number} radiusMeters - Maximum allowed distance from center
     * @param {Array} lineStrings - Optional LineStrings for snapping
     * @returns {Object|null} - Validated/snapped position or null if invalid
     */
    validatePosition(position, centerLat, centerLng, radiusMeters, lineStrings = null) {
        try {
            // Check distance from center
            const distance = turf.distance(
                [centerLng, centerLat],
                [position.lng, position.lat],
                { units: 'meters' }
            );

            if (distance > radiusMeters) {
                logger.debug(`[OSM-WAYS] Position rejected: ${distance.toFixed(1)}m > ${radiusMeters}m from center`);
                return null;
            }

            // If LineStrings provided, snap to nearest line
            if (lineStrings && lineStrings.length > 0) {
                const snapped = this.snapPointToLine(position, lineStrings);
                if (snapped) {
                    // Re-validate snapped position distance
                    const snappedDistance = turf.distance(
                        [centerLng, centerLat],
                        [snapped.lng, snapped.lat],
                        { units: 'meters' }
                    );

                    if (snappedDistance <= radiusMeters) {
                        return {
                            ...snapped,
                            distanceFromCenter: snappedDistance
                        };
                    }
                }
            }

            // Return original position if no snapping needed or snapping failed
            return {
                ...position,
                distanceFromCenter: distance
            };

        } catch (error) {
            logger.warn(`[OSM-WAYS] Error validating position:`, error.message);
            return null;
        }
    }

    /**
     * Generate OSM-constrained positions for trainer spawning
     * @param {number} centerLat - Center latitude
     * @param {number} centerLng - Center longitude
     * @param {number} count - Number of positions to generate (default: 5)
     * @param {number} radius - Search radius in meters (default: 500)
     * @param {number} minSpacing - Minimum spacing between positions (default: 20)
     * @returns {Promise<Array>} - Array of positions {lat, lng, lineString?, weight?, distanceFromCenter}
     */
    async generateOSMConstrainedPositions(centerLat, centerLng, count = 5, radius = 500, minSpacing = 20) {
        logger.debug(`[OSM-WAYS] Generating ${count} OSM-constrained positions within ${radius}m, min spacing ${minSpacing}m`);

        try {
            // 1. Fetch OSM ways data
            const osmData = await this.fetchOSMWays(centerLat, centerLng, radius);

            if (!osmData || !osmData.elements || osmData.elements.length === 0) {
                logger.warn('[OSM-WAYS] No OSM ways found, falling back to random positioning');
                return this.generateFallbackPositions(centerLat, centerLng, count, radius, minSpacing);
            }

            // 2. Convert to LineStrings
            const lineStrings = this.toLineStrings(osmData);

            if (lineStrings.length === 0) {
                logger.warn('[OSM-WAYS] No valid LineStrings created, falling back to random positioning');
                return this.generateFallbackPositions(centerLat, centerLng, count, radius, minSpacing);
            }

            // 3. Try sampling with current minimum spacing
            let positions = this.samplePointsOnLines(
                lineStrings,
                count,
                minSpacing,
                centerLat,
                centerLng,
                radius,
                this.trainerTagWeights
            );

            // 4. If insufficient positions, try with reduced spacing
            if (positions.length < count && minSpacing > this.minSpacingFallback) {
                logger.warn(`[OSM-WAYS] Only ${positions.length}/${count} positions with ${minSpacing}m spacing, trying ${this.minSpacingFallback}m`);

                positions = this.samplePointsOnLines(
                    lineStrings,
                    count,
                    this.minSpacingFallback,
                    centerLat,
                    centerLng,
                    radius,
                    this.trainerTagWeights
                );
            }

            // 5. If still insufficient, fill with fallback positions
            if (positions.length < count) {
                logger.warn(`[OSM-WAYS] Only ${positions.length}/${count} positions on OSM ways, filling with fallback positions`);

                const needed = count - positions.length;
                const fallbackPositions = this.generateFallbackPositions(
                    centerLat,
                    centerLng,
                    needed,
                    radius,
                    this.minSpacingFallback,
                    positions // Existing positions to avoid
                );

                positions = positions.concat(fallbackPositions);
            }

            logger.debug(`[OSM-WAYS] Generated ${positions.length}/${count} total positions (${positions.filter(p => p.lineString).length} on OSM ways)`);
            return positions.slice(0, count); // Ensure exact count

        } catch (error) {
            logger.error('[OSM-WAYS] Error generating OSM-constrained positions:', error);
            logger.warn('[OSM-WAYS] Falling back to random positioning due to error');
            return this.generateFallbackPositions(centerLat, centerLng, count, radius, minSpacing);
        }
    }

    /**
     * Generate fallback positions using random positioning when OSM data is insufficient
     * @param {number} centerLat - Center latitude
     * @param {number} centerLng - Center longitude
     * @param {number} count - Number of positions to generate
     * @param {number} radius - Search radius in meters
     * @param {number} minSpacing - Minimum spacing between positions
     * @param {Array} existingPositions - Existing positions to avoid (optional)
     * @returns {Array} - Array of fallback positions
     */
    generateFallbackPositions(centerLat, centerLng, count, radius, minSpacing, existingPositions = []) {
        logger.debug(`[OSM-WAYS] Generating ${count} fallback positions with ${minSpacing}m spacing`);

        const positions = [];
        const maxAttempts = count * 20; // Prevent infinite loops
        let attempts = 0;

        while (positions.length < count && attempts < maxAttempts) {
            attempts++;

            try {
                // Generate random position within radius
                const randomPosition = this.generateRandomPosition(centerLat, centerLng, radius);

                // Check spacing against existing positions (both OSM and fallback)
                const allExistingPositions = [...existingPositions, ...positions];
                let tooClose = false;

                for (const existingPos of allExistingPositions) {
                    const distance = turf.distance(
                        [randomPosition.lng, randomPosition.lat],
                        [existingPos.lng, existingPos.lat],
                        { units: 'meters' }
                    );

                    if (distance < minSpacing) {
                        tooClose = true;
                        break;
                    }
                }

                if (!tooClose) {
                    // Calculate distance from center for consistency
                    const distanceFromCenter = turf.distance(
                        [centerLng, centerLat],
                        [randomPosition.lng, randomPosition.lat],
                        { units: 'meters' }
                    );

                    positions.push({
                        lat: randomPosition.lat,
                        lng: randomPosition.lng,
                        lineString: null, // No associated OSM way
                        weight: null,
                        distanceFromCenter,
                        isFallback: true
                    });
                }

            } catch (error) {
                logger.warn(`[OSM-WAYS] Error generating fallback position:`, error.message);
                continue;
            }
        }

        logger.debug(`[OSM-WAYS] Generated ${positions.length}/${count} fallback positions after ${attempts} attempts`);
        return positions;
    }

    /**
     * Enhanced generateRandomPosition with configurable radius (extends existing method)
     * @param {number} centerLat - Center latitude
     * @param {number} centerLng - Center longitude
     * @param {number} radius - Radius in meters (default: this.spawnRadius)
     * @returns {Object} - {lat, lng} position
     */
    generateRandomPosition(centerLat, centerLng, radius = this.spawnRadius) {
        try {
            // Use turf.js for accurate positioning
            const randomAngle = Math.random() * 360;
            const distance = Math.random() * radius;

            const dest = turf.destination(
                [centerLng, centerLat],
                distance / 1000, // turf uses km
                randomAngle
            );

            return {
                lat: dest.geometry.coordinates[1],
                lng: dest.geometry.coordinates[0]
            };
        } catch (error) {
            logger.error('Error using turf.js for trainer position:', error);
            // Fallback to simple offset calculation
            const latOffset = (Math.random() - 0.5) * (radius / 111320);
            const lngOffset = (Math.random() - 0.5) * (radius / (111320 * Math.cos(centerLat * Math.PI / 180)));

            return {
                lat: centerLat + latOffset,
                lng: centerLng + lngOffset
            };
        }
    }

    /**
     * Spawn multiple trainers using OSM-constrained positioning
     * @param {number} playerLat - Player's latitude
     * @param {number} playerLng - Player's longitude
     * @param {number} count - Number of trainers to spawn (default: 5)
     * @returns {Promise<Array>} - Array of spawned trainers
     */
    async spawnRandomTrainers(playerLat, playerLng, count = 5) {
        logger.info(`[TRAINER-SPAWNER] 🎯 Starting spawn of ${count} trainers within ${this.spawnRadius}m radius`);

        try {
            // Ensure data is loaded
            await this.loadData();

            // Generate OSM-constrained positions
            const positions = await this.generateOSMConstrainedPositions(
                playerLat,
                playerLng,
                count,
                this.spawnRadius,
                this.minSpacing
            );

            if (positions.length === 0) {
                logger.warn('[TRAINER-SPAWNER] No valid positions generated for trainers');
                return [];
            }

            logger.debug(`[TRAINER-SPAWNER] Generated ${positions.length} positions, creating trainers...`);

            // Create trainers for each position
            const trainers = [];
            for (let i = 0; i < positions.length; i++) {
                const position = positions[i];

                try {
                    // Create trainer with position override
                    const trainer = await this.spawnRandomTrainer(playerLat, playerLng, position);

                    if (trainer) {
                        trainers.push(trainer);

                        // Log spawn details
                        const positionType = position.lineString ? 'OSM way' : 'fallback';
                        const weight = position.weight ? ` (weight: ${position.weight})` : '';
                        logger.debug(`[TRAINER-SPAWNER] ✅ Spawned trainer ${i + 1}/${positions.length}: ${trainer.name} on ${positionType}${weight}`);
                    } else {
                        logger.warn(`[TRAINER-SPAWNER] ❌ Failed to create trainer ${i + 1}/${positions.length}`);
                    }
                } catch (error) {
                    logger.error(`[TRAINER-SPAWNER] Error creating trainer ${i + 1}:`, error);
                    continue;
                }
            }

            // Note: lastTrainerSpawnAnchor is now updated by the calling function after successful spawn

            logger.info(`[TRAINER-SPAWNER] 🎉 Successfully spawned ${trainers.length}/${count} trainers`);
            return trainers;

        } catch (error) {
            logger.error('[TRAINER-SPAWNER] Error in spawnRandomTrainers:', error);
            return [];
        }
    }

    /**
     * Enhanced spawnRandomTrainer with position override support
     * @param {number} playerLat - Player's latitude
     * @param {number} playerLng - Player's longitude
     * @param {Object} positionOverride - Optional position override {lat, lng}
     * @returns {Promise<Trainer|null>} - The spawned trainer or null if failed
     */
    async spawnRandomTrainer(playerLat, playerLng, positionOverride = null) {
        try {
            // Ensure data is loaded
            await this.loadData();

            // Get random trainer type
            const trainerTypeKeys = Object.keys(this.trainerTypes);
            const randomTypeKey = trainerTypeKeys[Math.floor(Math.random() * trainerTypeKeys.length)];
            const trainerTypeData = this.trainerTypes[randomTypeKey];

            // Get random variant
            const variants = trainerTypeData.sprites.variants;
            const randomVariant = variants[Math.floor(Math.random() * variants.length)];

            // Get appropriate name based on gender
            const names = this.npcNames[randomVariant.gender];
            const randomName = names[Math.floor(Math.random() * names.length)];

            // Generate team based on preferences
            const team = await this.generateTeam(trainerTypeData.preferences, trainerTypeData.levelRange);

            // Create trainer
            const trainer = new Trainer(
                randomTypeKey,
                randomVariant,
                randomName,
                team,
                trainerTypeData.levelRange
            );

            // Set position (use override if provided, otherwise generate random)
            let position;
            if (positionOverride) {
                position = positionOverride;
            } else {
                position = this.generateRandomPosition(playerLat, playerLng);
            }

            trainer.setPosition(position.lat, position.lng);

            logger.debug(`[TRAINER-SPAWNER] Created trainer: ${trainer.name} (${trainer.trainerType}) at ${position.lat}, ${position.lng}`);
            return trainer;

        } catch (error) {
            logger.error('[TRAINER-SPAWNER] Error spawning random trainer:', error);
            return null;
        }
    }
}

// Export singleton instance
export const trainerSpawner = new TrainerSpawner();
